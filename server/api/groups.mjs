import Group from "../models/group.mjs";
import User from "../models/user.mjs"
import Invoice from "../models/invoice.mjs";


//helper functions
function parseDuration(duration) {
  // Remove any parentheses
  //console.log('parsing duration: ', duration)
  duration = duration.replace(/[()]/g, '');
  let hasDuration = true
  // Handle both colon and dot as separators
  let parts;
  if (duration.includes(':')) {
    parts = duration.split(':');
  } else if (duration.includes('.')) {
    parts = duration.split('.');
  } else {
    // console.log(duration);
    //console.log('has no duration')
    hasDuration = false
    // throw new Error('Invalid duration format');
  }
  if (hasDuration) {
    const minutes = parseInt(parts[0], 10);
    const seconds = parseInt(parts[1], 10);
    return (minutes * 60) + seconds;
  } else {
    //console.log('returning hasDuration: ', hasDuration)
    return hasDuration
  }
}

function getTotalDuration(groups) {
  let totalSeconds = 0;
  let notice = []
  groups.forEach(group => {
    group.tracks.forEach((track, index) => {
      let seconds = parseDuration(track.duration)
      //console.log('parseDuration result: ', seconds)
      if (seconds === false) {
        notice.push(`${group.schoolName} - Selection ${index + 1} does not have a duration`)
      }
      totalSeconds += parseDuration(track.duration);
    });
  });

  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  const formattedHours = hours.toString().padStart(2, '0');
  const formattedMinutes = minutes.toString().padStart(2, '0');
  const formattedSeconds = seconds.toString().padStart(2, '0');
  let durationString = `${formattedHours}:${formattedMinutes}:${formattedSeconds}`
  let returnData = {
    duration: durationString
  }
  if (notice.length > 0) {
    returnData.duration += '*'
    returnData.notice = notice
  }
  return returnData
}

async function extractDownloadCodes(csv) {
  console.log(csv)
  console.log('I am here extractDownloadCodes')
  const rows = csv.split('\n');
  let collectedCodes = [];
  let isCodeSection = false;

  rows.forEach(row => {
    const columns = row.split(',');
    console.log('columns')
    console.log(columns[0]);
    if (columns[0] === 'DOWNLOAD CODES') {
      console.log('isCodeSection bool', true)
      isCodeSection = true;
      return;
    }

    if (isCodeSection) {
      columns.forEach((column, index) => {
        if (column) {
          if (!collectedCodes[index]) collectedCodes[index] = [];
          collectedCodes[index].push(column);
        }
      });
    }
  });

  const flattenedCodes = [].concat.apply([], collectedCodes);
  console.log(flattenedCodes)
  return flattenedCodes;
}

//routes
export default async function (fastify, opts) {
  // Get a single group by ID
  fastify.get(`/api/groups/:id`, async (request, reply) => {
    try {
      const groupId = request.params.id;
      const group = await Group.findById(groupId)
        .populate([
          { path: "contest", select: "name" },
          { path: "category", select: "name" },
          { path: "classification", select: "name" },
          { path: "classifications.id", select: "name maskedName" },
          {
            path: "tracks",
            select: "title composer duration venue minioSrc memos soloists",
            populate: {
              path: "soloists",
              select: "name instrument"
            }
          }
        ])
        .lean();

      if (!group) {
        return reply.code(404).send({ error: "Group not found" });
      }

      reply.code(200).send(group);
    } catch (err) {
      console.error(err);
      reply.code(500).send({ error: "Server error" });
    }
  });

  //post a group
  fastify.post(`/api/groups`, async (request, reply) => {
    try {
      const group = request.body
      const newGroup = new Group(group);
      await newGroup.save();
      await User.findByIdAndUpdate(
        group.userId,
        { $push: { groups: newGroup._id } },
        { new: true }
      );
      console.log('New Group: ', newGroup);
      reply.code(200).send(newGroup._id);
    } catch (err) {
      console.error(err);
      reply.code(500).send(err);
    }
  });

  fastify.get(`/api/groups`, async (request, reply) => {
    console.log("trying to get groups");
    if (request.query.category) {
      const category = request.query.category
      const year = request.query?.year ? request.query.year : null
      const isArchived = request.query?.isArchived ? request.query.isArchived : false
      let classification = request.query?.classification ? request.query.classification : null
      let filter = { category, year, isArchived }
      //check if category is national wind band
      if (classification !== 'all') {
        if (category === '643456dbdd98890aad8cef31') {
          //console.log('windband', classification)
          filter['classifications.id'] = classification
          //console.log(filter)
        } else {
          filter.classification = classification
        }
      }

      //console.log('category query', category)
      //console.log('filter', filter)
      const groups = await Group.find(filter)
        .populate([
          { path: "contest", select: "name" },
          { path: "category", select: "name" },
          { path: "classification", select: "name" },
          { path: "classifications.id", select: "name maskedName" },
          {
            path: "tracks",
            select: "title composer duration venue minioSrc",
            populate: {
              path: "soloists",
              select: "name instrument"
            }
          }
        ])
        .sort({ "classification": 1 })
        .lean()
      // console.log(groups)
      let totalDuration = getTotalDuration(groups)
      // console.log('fetchEntries', totalDuration)
      let data = {
        groups: groups,
        totalDuration: totalDuration
      }
      reply.send(data)
    }
    const groups = await Group.find()
      .populate([
        { path: "contest", select: "name -_id" },
        { path: "category", select: "name -_id" },
        { path: "classification", select: "name -_id" },
        { path: "tracks", select: "-__v" },
      ])
      .select('-__v')
      .lean();
    for (const group of groups) {
      group.contest = group.contest.name;
      group.category = group.category.name;
      group.classification = group.classification?.name;
      //group.tracks = group.tracks.map((track) => track._id);
    }
    reply.send(groups);
  });

  fastify.put(`/api/groups/adminUpdate`, async (request, reply) => {
    console.log('route hit')
    const { filter, toUpdate } = { ...request.body }
    console.log(toUpdate)
    const group = await Group.findOneAndUpdate(filter, toUpdate, {
      new: true
    }).populate([
      { path: "contest", select: "name" },
      { path: "category", select: "name" },
      { path: "classification", select: "name" },
      { path: "classifications.id", select: "name maskedName" },
      {
        path: "tracks",
        select: "title composer duration venue minioSrc",
        populate: {
          path: "soloists",
          select: "name instrument"
        }
      }
    ])
      .lean()
    console.log(group)
    reply.send(group)
  })
  // fastify.get('/api/getUsersGroups/:id', async(request,reply)=>{

  // })
  fastify.post(`/api/groups/updateOldGroupScore`, async (request, reply) => {
    let group = request.body
    try {
      let data = await Group.findOneAndUpdate({ _id: group._id }, { score: group.score })
      reply.send('group updated').code(200)
    } catch (error) {
      console.log(error)
    }
  })

  fastify.put('/api/groups/updateGroupScore', async (request, reply) => {
    const { groupId, judgeId, score, movesOn, isCommended, isNational, isCitation, isState, classificationIndex } = request.body;

    const updateData = {
      judgeId,
      score,
      movesOn,
      isCommended,
      isNational,
      isCitation,
      isState
    };

    try {
      let updatedGroup;

      if (classificationIndex !== undefined && classificationIndex !== null && classificationIndex >= 0) {
        // Handle classification-based judge scores
        console.log(`Updating classification ${classificationIndex} judge scores for group ${groupId}, judge ${judgeId}`);

        // First try to update existing judge score in the classification
        updatedGroup = await Group.findOneAndUpdate(
          {
            _id: groupId,
            [`classifications.${classificationIndex}.judgeScores.judgeId`]: judgeId
          },
          {
            $set: { [`classifications.${classificationIndex}.judgeScores.$`]: updateData }
          },
          {
            new: true,
            runValidators: true
          }
        );

        // If no existing judge score found in the classification, add a new one
        if (!updatedGroup) {
          console.log(`No existing judge score found, adding new score to classification ${classificationIndex}`);
          updatedGroup = await Group.findOneAndUpdate(
            { _id: groupId },
            {
              $push: { [`classifications.${classificationIndex}.judgeScores`]: updateData }
            },
            {
              new: true,
              runValidators: true
            }
          );
        }
      } else {
        // Handle regular group judge scores (non-classification based)
        console.log(`Updating regular group judge scores for group ${groupId}, judge ${judgeId}`);

        // First try to update existing judge score
        updatedGroup = await Group.findOneAndUpdate(
          {
            _id: groupId,
            'judgeScores.judgeId': judgeId
          },
          {
            $set: { 'judgeScores.$': updateData }
          },
          {
            new: true,
            runValidators: true
          }
        );

        // If no existing judge score found, add a new one
        if (!updatedGroup) {
          console.log(`No existing judge score found, adding new score to group`);
          updatedGroup = await Group.findOneAndUpdate(
            { _id: groupId },
            {
              $addToSet: { judgeScores: updateData }
            },
            {
              new: true,
              runValidators: true
            }
          );
        }
      }

      // Populate the necessary fields
      await updatedGroup.populate([
        { path: "contest", select: "name" },
        { path: "category", select: "name" },
        { path: "classification", select: "name" },
        { path: "classifications.id", select: "name maskedName" },
        {
          path: "tracks",
          select: "title composer duration venue minioSrc memos soloists",
          populate: {
            path: "soloists",
            select: "name instrument timeStamp"
          }
        }
      ]);

      if (!updatedGroup) {
        return reply.code(404).send({ error: 'Group not found' });
      }

      reply.code(200).send(updatedGroup);

    } catch (error) {
      console.error(error);
      reply.code(500).send({ error: 'An error occurred while updating the group' });
    }
  });


  fastify.post('/api/groups/uploadCodes', async (request, reply) => {
    try {
      const data = await request.file();
      const year = request.body?.year || new Date().getFullYear(); // Default to current year if not provided

      const buffer = await data.toBuffer();
      const csvContent = buffer.toString('utf-8');

      // Extract download codes from the CSV content
      const codes = await extractDownloadCodes(csvContent);

      // Fetch only groups for the specified year and where isArchived is false
      const allGroups = await Group.find({ year, isArchived: false });
      let modifiedGroups = [];

      for (let group of allGroups) {
        console.log('Total codes available:', codes.length);

        let updatedFields = {};

        if (group.classifications && group.classifications.length) {
          for (let classification of group.classifications) {
            const nextCode = codes.shift(); // Take the next available code

            if (!nextCode) {
              console.log('No codes left');
              break;
            }

            classification.downloadCode = nextCode; // Assign download code
          }
          updatedFields.classifications = group.classifications;
        } else {
          const nextCode = codes.shift();
          if (nextCode) {
            updatedFields.downloadCode = nextCode;
          }
        }

        if (Object.keys(updatedFields).length) {
          await Group.updateOne({ _id: group._id }, { $set: updatedFields });
          modifiedGroups.push(group._id);
        }
      }

      // Response to show unused codes if any
      if (codes.length > 0) {
        reply.send({
          message: `CSV processed for year ${year} and groups updated.`,
          unusedCodes: codes
        });
      } else {
        reply.send({
          message: `CSV processed for year ${year} and groups updated. All codes have been used.`
        });
      }
    } catch (error) {
      console.error('Error while processing file:', error);
      reply.status(500).send({ message: "Error occurred while processing file.", error: error.message });
    }
  });

  fastify.get(`/api/invoice/:id`, async (request, reply) => {
    const { id } = request.params;  // Retrieve the invoice ID from the request parameters

    try {
      // Find the invoice by its ObjectId
      const invoice = await Invoice.findById(id);

      // If the invoice is not found, return a 404 error
      if (!invoice) {
        return reply.code(404).send({ error: 'Invoice not found' });
      }

      // Send the invoice data in the response
      reply.send(invoice);
    } catch (error) {
      // If an error occurs during the process, return a 500 error
      console.error(error);
      reply.code(500).send({ error: 'An error occurred while fetching the invoice' });
    }
  });
}

