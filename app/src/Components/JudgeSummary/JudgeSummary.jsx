import React, { useContext, useEffect, useState } from 'react';
import { AppProviderStore } from '../../AppStore';
import { observer } from 'mobx-react';

// Import the extracted components
import {
  SummaryView,
  ClassificationSummaryView,
  GroupsView
} from './components';

const JudgeSummary = ({ handleSelect }) => {
  const { AppStore } = useContext(AppProviderStore);
  const { assignments, judge } = AppStore;
  const [summaryData, setSummaryData] = useState([]);
  const [currentView, setCurrentView] = useState('summary'); // 'summary', 'classificationSummary', or 'groups'
  const [selectedClassification, setSelectedClassification] = useState(null);

  // State for sorting groups
  const [sortConfig, setSortConfig] = useState({
    key: 'maskedName',
    direction: 'asc'
  });

  // State for processed groups with score information
  const [processedGroups, setProcessedGroups] = useState([]);

  // State for loading indicators
  const [isAutoSelectLoading, setIsAutoSelectLoading] = useState(false);

  // Function to handle sorting
  const requestSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Function to get score for a group
  const getGroupScore = (group, classification = selectedClassification) => {
    let score = null;

    if (!group.classifications || !group.classifications.length) {
      // Check for score in group.judgeScores
      const judgeScore = group.judgeScores?.find(score => score.judgeId === judge?._id);
      if (judgeScore && judgeScore.score !== null && judgeScore.score !== undefined && judgeScore.score !== "") {
        score = parseFloat(judgeScore.score);
      }
    } else if (classification) {
      // Find the classification that matches
      // Handle both cases: classification.id might be a string or an object with _id
      const classificationId = typeof classification.id === 'string'
        ? classification.id
        : classification.id?._id;

      const matchingClassification = group.classifications.find(c => {
        if (typeof c.id === 'string') {
          return c.id === classificationId;
        } else if (typeof c.id === 'object' && c.id._id) {
          return c.id._id === classificationId;
        }
        return false;
      });

      if (matchingClassification?.judgeScores) {
        const judgeScore = matchingClassification.judgeScores.find(score => score.judgeId === judge?._id);
        if (judgeScore && judgeScore.score !== null && judgeScore.score !== undefined && judgeScore.score !== "") {
          score = parseFloat(judgeScore.score);
        }
      }
    }

    return score;
  };

  // Function to check if a group is marked as "Moves On"
  const getGroupMovesOn = (group, classification = selectedClassification) => {
    let movesOn = false;

    if (!group.classifications || !group.classifications.length) {
      // Check for movesOn in group.judgeScores
      const judgeScore = group.judgeScores?.find(score => score.judgeId === judge?._id);
      if (judgeScore) {
        movesOn = Boolean(judgeScore.movesOn);
      }
    } else if (classification) {
      // Find the classification that matches
      const classificationId = typeof classification.id === 'string'
        ? classification.id
        : classification.id?._id;

      const matchingClassification = group.classifications.find(c => {
        if (typeof c.id === 'string') {
          return c.id === classificationId;
        } else if (typeof c.id === 'object' && c.id._id) {
          return c.id._id === classificationId;
        }
        return false;
      });

      if (matchingClassification?.judgeScores) {
        const judgeScore = matchingClassification.judgeScores.find(score => score.judgeId === judge?._id);
        if (judgeScore) {
          movesOn = Boolean(judgeScore.movesOn);
        }
      }
    }

    return movesOn;
  };

  // Function to calculate which groups are in the top 50% by score
  const calculateTopGroups = (groups) => {
    // Filter groups that have valid scores
    const scoredGroups = groups.filter(group =>
      group.score !== null && !isNaN(group.score) && group.score > 0
    );

    // Sort groups by score in descending order
    const sortedGroups = [...scoredGroups].sort((a, b) => b.score - a.score);

    // Calculate how many groups make up the top 50%
    const topCount = Math.ceil(sortedGroups.length / 2);

    // Get the minimum score that qualifies for top 50%
    const minTopScore = sortedGroups.length > 0 && topCount > 0
      ? sortedGroups[Math.min(topCount - 1, sortedGroups.length - 1)].score
      : 0;

    // Create a Set of group IDs in the top 50%
    const topGroupIds = new Set();
    sortedGroups.forEach((group, index) => {
      if (index < topCount || group.score >= minTopScore) {
        topGroupIds.add(group._id);
      }
    });

    return {
      topGroupIds,
      minTopScore,
      topCount
    };
  };

  // Function to process groups with score information
  const processGroups = (groups, classification = selectedClassification) => {
    // First, process basic group information
    const processedGroups = groups.map(group => {
      // Get the masked name
      const maskedName = group.maskedName ||
        (group.classifications && classification && group.classifications.find(c => {
          const classificationId = typeof classification.id === 'string' ? classification.id : classification.id?._id;
          if (typeof c.id === 'string') {
            return c.id === classificationId;
          } else if (typeof c.id === 'object' && c.id._id) {
            return c.id._id === classificationId;
          }
          return false;
        })?.maskedName) || '';

      // Get the score
      const score = getGroupScore(group, classification);

      // Get the movesOn status
      const movesOn = getGroupMovesOn(group, classification);

      // Determine if the group has been scored
      const hasScore = score !== null && !isNaN(score) && score > 0;

      return {
        ...group,
        maskedName,
        score,
        hasScore,
        movesOn,
        isTopScore: false // Will be set in the next step
      };
    });

    // Calculate which groups are in the top 50%
    const { topGroupIds } = calculateTopGroups(processedGroups);

    // Mark groups that are in the top 50%
    return processedGroups.map(group => ({
      ...group,
      isTopScore: topGroupIds.has(group._id)
    }));
  };

  // Function to handle clicking on a classification
  const handleClassificationClick = (classification, categoryId) => {
    console.log('Classification clicked:', classification);

    // Find the original classification data from assignments
    const category = assignments.find(a => a.category?._id === categoryId);
    if (!category) {
      console.error('Category not found:', categoryId);
      return;
    }

    // The classification.id is already the string ID from the processed data
    const originalClassification = category.classifications.find(c => c.id?._id === classification.id);
    if (!originalClassification) {
      console.error('Classification not found in assignments:', classification.id);
      return;
    }

    // Check if the classification has groups
    if (!originalClassification.groups || originalClassification.groups.length === 0) {
      console.log('No groups available in this classification');
      return;
    }

    // Set the selected classification
    setSelectedClassification(originalClassification);

    // Update the AppStore with the selected category and classification
    AppStore.setCurrentSelectedCategory(category);
    AppStore.setCurrentSelectedClassification(originalClassification);

    // Process the groups with score information
    const processed = processGroups(originalClassification.groups, originalClassification);
    setProcessedGroups(processed);

    // Change the view to show classification summary
    setCurrentView('classificationSummary');
  };

  // Function to handle selecting a group
  const handleGroupSelect = (group) => {
    console.log('JudgeSummary: Group selected:', group._id);

    // Set up the classification context in AppStore if we have a selected classification
    if (selectedClassification) {
      // Find the classification index in the group's classifications array
      let classificationIndex = -1;

      if (group.classifications && group.classifications.length > 0) {
        // Handle both cases: when classification.id is a string or an object
        const classificationId = typeof selectedClassification.id === 'string'
          ? selectedClassification.id
          : selectedClassification.id?._id;

        classificationIndex = group.classifications.findIndex(c => {
          if (typeof c.id === 'string') {
            return c.id === classificationId;
          } else if (typeof c.id === 'object' && c.id._id) {
            return c.id._id === classificationId;
          }
          return false;
        });

        console.log(`JudgeSummary: Found classification index ${classificationIndex} for group ${group._id}`);
      }

      // Set the classification index in AppStore
      AppStore.selectedClassificationIndex = classificationIndex;

      // Load the existing judge score from the correct location
      let existingScore = null;
      if (classificationIndex >= 0 && group.classifications[classificationIndex]?.judgeScores) {
        existingScore = group.classifications[classificationIndex].judgeScores.find(
          score => score.judgeId === judge?._id
        );
      } else if (!group.classifications?.length && group.judgeScores) {
        existingScore = group.judgeScores.find(score => score.judgeId === judge?._id);
      }

      // Set the judge score in AppStore
      const scoreToSet = existingScore || {
        judgeId: judge?._id,
        score: null,
        movesOn: false,
        isCommended: false,
        isNational: false,
        isCitation: false,
        isState: false
      };

      AppStore.setJudgeScore(scoreToSet);
      console.log('JudgeSummary: Set judge score:', scoreToSet);
    }

    if (handleSelect && typeof handleSelect === 'function') {
      console.log('JudgeSummary: Calling parent handleSelect function');
      handleSelect(group);
    } else {
      console.log('JudgeSummary: No parent handleSelect function provided');
    }
  };

  // Function to handle "Moves On" checkbox changes
  const handleMovesOnChange = async (group, checked) => {
    console.log('JudgeSummary: Moves On changed for group:', group._id, 'to:', checked);

    try {
      // Find the classification index for this group
      let classificationIndex = -1;
      let existingScore = null;

      if (!group.classifications || !group.classifications.length) {
        // No classifications, use regular group judge scores
        existingScore = group.judgeScores?.find(score => score.judgeId === judge?._id);
      } else if (selectedClassification) {
        // Find the classification index
        const classificationId = typeof selectedClassification.id === 'string'
          ? selectedClassification.id
          : selectedClassification.id?._id;

        classificationIndex = group.classifications.findIndex(c => {
          if (typeof c.id === 'string') {
            return c.id === classificationId;
          } else if (typeof c.id === 'object' && c.id._id) {
            return c.id._id === classificationId;
          }
          return false;
        });

        console.log(`JudgeSummary: Found classification index ${classificationIndex} for moves on change`);

        if (classificationIndex >= 0 && group.classifications[classificationIndex]?.judgeScores) {
          existingScore = group.classifications[classificationIndex].judgeScores.find(
            score => score.judgeId === judge?._id
          );
        }
      }

      // Create a score object with the updated movesOn value
      const scoreObj = {
        score: existingScore?.score || 0, // Use existing score or default to 0
        movesOn: checked,
        isCommended: existingScore?.isCommended || false,
        isNational: existingScore?.isNational || false,
        isCitation: existingScore?.isCitation || false,
        isState: existingScore?.isState || false
      };

      // Set the classification index in AppStore
      AppStore.selectedClassificationIndex = classificationIndex;

      // Update the score in AppStore
      AppStore.setJudgeScore(scoreObj);

      // Save the group with the current group selected
      const originalSelectedGroup = AppStore.selectedGroup;
      const originalClassificationIndex = AppStore.selectedClassificationIndex;

      AppStore.setGroup(group);
      AppStore.selectedClassificationIndex = classificationIndex;

      // Update the score in the database
      const updatedGroup = await AppStore.setGroupScore();

      // Restore the original selected group and classification index if needed
      if (originalSelectedGroup && originalSelectedGroup._id !== group._id) {
        AppStore.setGroup(originalSelectedGroup);
        AppStore.selectedClassificationIndex = originalClassificationIndex;
      }

      // Update the processed groups with the new data
      if (updatedGroup) {
        const updatedProcessedGroups = processedGroups.map(g =>
          g._id === updatedGroup._id ? { ...g, movesOn: checked } : g
        );
        setProcessedGroups(updatedProcessedGroups);
      }

      return true;
    } catch (error) {
      console.error('Error updating Moves On status:', error);
      return false;
    }
  };

  // Function to automatically select all top groups to move on
  const handleAutoSelectTopGroups = async () => {
    console.log('Auto-selecting all top groups to move on');

    try {
      // Set loading state
      setIsAutoSelectLoading(true);

      // Get all top groups that aren't already selected to move on
      const topGroupsToUpdate = processedGroups.filter(group =>
        group.isTopScore && !group.movesOn && group.hasScore
      );

      if (topGroupsToUpdate.length === 0) {
        console.log('No top groups to update');
        setIsAutoSelectLoading(false);
        return;
      }

      console.log(`Updating ${topGroupsToUpdate.length} groups to move on`);

      // Process each group sequentially
      for (const group of topGroupsToUpdate) {
        await handleMovesOnChange(group, true);
      }

      // Show success message or update UI as needed
      console.log('Successfully updated all top groups');

    } catch (error) {
      console.error('Error auto-selecting top groups:', error);
    } finally {
      // Reset loading state
      setIsAutoSelectLoading(false);
    }
  };

  useEffect(() => {
    console.log('Assignments data:', assignments);
    if (assignments && Array.isArray(assignments) && assignments.length > 0) {
      // Process the assignments data to create summary information
      const processedData = assignments.map(assignment => {
        const categoryName = assignment.category?.name;
        const categoryId = assignment.category?._id;

        // Process classifications under this category
        const classificationsData = assignment.classifications?.map(classification => {
          // Count scored vs unscored groups
          const totalGroups = classification.groups ? classification.groups.length : 0;
          const scoredGroups = classification.groups ? classification.groups.filter(group => {
            // Check if this group has been scored by the current judge
            let isScored = false;

            if (!group.classifications || !group.classifications.length) {
              isScored = group.judgeScores &&
                Array.isArray(group.judgeScores) &&
                group.judgeScores.some(score => {
                  // A score is only valid if it has a numeric value greater than 0
                  // We need to handle JavaScript type coercion where empty strings can become 0
                  // First check if the score belongs to the current judge
                  const isCurrentJudge = score.judgeId === judge?._id;

                  // Then check if the score has a valid numeric value
                  const hasNumericValue = score.score !== null &&
                    score.score !== undefined &&
                    score.score !== "" &&
                    !isNaN(score.score);

                  // Finally check if the score is greater than 0
                  // Note: We're explicitly checking for score > 0 to avoid counting 0 scores as valid
                  const isPositive = hasNumericValue && parseFloat(score.score) > 0;

                  // A score is only valid if it meets all criteria
                  const hasValidScore = isCurrentJudge && hasNumericValue && isPositive;

                  if (hasValidScore) {
                    console.log(`Group ${group._id} has valid score: ${score.score} (${typeof score.score})`);
                  } else if (isCurrentJudge) {
                    let reason = "";
                    if (!hasNumericValue) {
                      reason = "Not a valid number";
                    } else if (!isPositive) {
                      reason = "Score is not greater than 0";
                    }
                    console.log(`Group ${group._id} has INVALID score: ${score.score} (${typeof score.score}), reason: ${reason}, value: ${JSON.stringify(score)}`);
                  }

                  return hasValidScore;
                });
            } else {
              // Find the classification that matches - handle both string and object IDs
              const classificationId = classification.id?._id;
              const matchingClassification = group.classifications.find(c => {
                if (typeof c.id === 'string') {
                  return c.id === classificationId;
                } else if (typeof c.id === 'object' && c.id._id) {
                  return c.id._id === classificationId;
                }
                return false;
              });

              isScored = matchingClassification &&
                matchingClassification.judgeScores &&
                Array.isArray(matchingClassification.judgeScores) &&
                matchingClassification.judgeScores.some(score => {
                  // A score is only valid if it has a numeric value greater than 0
                  // We need to handle JavaScript type coercion where empty strings can become 0
                  // First check if the score belongs to the current judge
                  const isCurrentJudge = score.judgeId === judge?._id;

                  // Then check if the score has a valid numeric value
                  const hasNumericValue = score.score !== null &&
                    score.score !== undefined &&
                    score.score !== "" &&
                    !isNaN(score.score);

                  // Finally check if the score is greater than 0
                  // Note: We're explicitly checking for score > 0 to avoid counting 0 scores as valid
                  const isPositive = hasNumericValue && parseFloat(score.score) > 0;

                  // A score is only valid if it meets all criteria
                  const hasValidScore = isCurrentJudge && hasNumericValue && isPositive;

                  if (hasValidScore) {
                    console.log(`Group ${group._id} in classification ${classification.id?._id} has valid score: ${score.score} (${typeof score.score})`);
                  } else if (isCurrentJudge) {
                    let reason = "";
                    if (!hasNumericValue) {
                      reason = "Not a valid number";
                    } else if (!isPositive) {
                      reason = "Score is not greater than 0";
                    }
                    console.log(`Group ${group._id} in classification ${classification.id?._id} has INVALID score: ${score.score} (${typeof score.score}), reason: ${reason}, value: ${JSON.stringify(score)}`);
                  }

                  return hasValidScore;
                });
            }

            if (!isScored) {
              console.log(`Group ${group._id} has NO valid score`);
            }

            return isScored;
          }).length : 0;

          console.log(`Classification ${classification.id?.name}: ${scoredGroups}/${totalGroups} groups scored`);

          // Determine if this classification is fully scored
          // Only consider it fully scored if there are groups and all of them have been scored
          const isFullyScored = totalGroups > 0 && scoredGroups > 0 && scoredGroups === totalGroups;

          return {
            id: classification.id?._id || 'unknown',
            name: classification.id?.name || 'Unknown Classification',
            phase: classification.phase || 'Not specified',
            totalGroups,
            scoredGroups,
            unscoredGroups: totalGroups - scoredGroups,
            progress: totalGroups > 0 ? (scoredGroups / totalGroups) * 100 : 0,
            isFullyScored
          };
        }) || [];

        // Calculate overall stats for this category
        const totalClassifications = classificationsData.length;

        // A classification is only counted as completed if it has groups and all of them are scored
        const completedClassifications = classificationsData.filter(c =>
          c.totalGroups > 0 && c.isFullyScored
        ).length;

        const remainingClassifications = totalClassifications - completedClassifications;

        // Calculate overall progress based on classifications
        const classificationProgress = totalClassifications > 0
          ? (completedClassifications / totalClassifications) * 100
          : 0;

        return {
          id: categoryId || 'unknown',
          name: categoryName || 'Unknown Category',
          classifications: classificationsData,
          totalClassifications,
          completedClassifications,
          remainingClassifications,
          progress: classificationProgress,
          // Keep group stats for detailed view
          totalGroups: classificationsData.reduce((sum, c) => sum + c.totalGroups, 0),
          scoredGroups: classificationsData.reduce((sum, c) => sum + c.scoredGroups, 0),
          unscoredGroups: classificationsData.reduce((sum, c) => sum + c.unscoredGroups, 0)
        };
      });

      setSummaryData(processedData);
    }
  }, [assignments, judge]);

  // Calculate overall judging progress
  const calculateOverallProgress = () => {
    if (!summaryData || summaryData.length === 0) return { progress: 0, total: 0, completed: 0 };

    // Count total classifications that have groups (we only care about classifications with groups)
    const totalClassifications = summaryData.reduce((sum, category) => {
      // Count classifications that have at least one group
      const classificationsWithGroups = category.classifications.filter(c => c.totalGroups > 0).length;
      return sum + classificationsWithGroups;
    }, 0);

    const completedClassifications = summaryData.reduce((sum, category) => sum + category.completedClassifications, 0);
    const progress = totalClassifications > 0 ? (completedClassifications / totalClassifications) * 100 : 0;

    console.log(`Overall progress: ${completedClassifications}/${totalClassifications} classifications completed (${Math.round(progress)}%)`);

    return {
      progress,
      total: totalClassifications,
      completed: completedClassifications,
      remaining: totalClassifications - completedClassifications
    };
  };

  const overallProgress = calculateOverallProgress();



  // Render the appropriate view based on currentView state
  return (
    <>
      {currentView === 'summary' && (
        <SummaryView
          judge={judge}
          summaryData={summaryData}
          overallProgress={overallProgress}
          onClassificationClick={handleClassificationClick}
        />
      )}

      {currentView === 'classificationSummary' && (
        <ClassificationSummaryView
          selectedClassification={selectedClassification}
          processedGroups={processedGroups}
          isAutoSelectLoading={isAutoSelectLoading}
          handleAutoSelectTopGroups={handleAutoSelectTopGroups}
          handleMovesOnChange={handleMovesOnChange}
          handleGroupSelect={handleGroupSelect}
          onBackToSummary={() => {
            setCurrentView('summary');
            // Clear the selected category and classification in AppStore
            AppStore.setCurrentSelectedCategory(null);
            AppStore.setCurrentSelectedClassification(null);
          }}
          onViewAllGroups={() => setCurrentView('groups')}
        />
      )}

      {currentView === 'groups' && (
        <GroupsView
          selectedClassification={selectedClassification}
          processedGroups={processedGroups}
          sortConfig={sortConfig}
          requestSort={requestSort}
          isAutoSelectLoading={isAutoSelectLoading}
          handleAutoSelectTopGroups={handleAutoSelectTopGroups}
          handleMovesOnChange={handleMovesOnChange}
          handleGroupSelect={handleGroupSelect}
          onBackToClassificationSummary={() => setCurrentView('classificationSummary')}
          onBackToSummary={() => {
            setCurrentView('summary');
            // Clear the selected category and classification in AppStore
            AppStore.setCurrentSelectedCategory(null);
            AppStore.setCurrentSelectedClassification(null);
          }}
        />
      )}
    </>
  );
};

export default observer(JudgeSummary);
