import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Edit as EditIcon,
  Close as CloseIcon,
  VideoCall as VideoIcon
} from '@mui/icons-material';

const JudgeIntroVideo = ({ 
  judge, 
  onEditClick, 
  showEditButton = true, 
  compact = false,
  title = "Your Introduction Video"
}) => {
  const [videoDialogOpen, setVideoDialogOpen] = useState(false);

  const handlePlayVideo = () => {
    setVideoDialogOpen(true);
  };

  const handleEditVideo = () => {
    if (onEditClick && typeof onEditClick === 'function') {
      onEditClick();
    }
  };

  if (!judge?.judgeIntro) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center', bgcolor: '#f5f5f5' }}>
        <VideoIcon sx={{ fontSize: 48, color: '#ccc', mb: 2 }} />
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No Introduction Video
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          You haven't recorded an introduction video yet.
        </Typography>
        {showEditButton && (
          <Button
            variant="contained"
            startIcon={<VideoIcon />}
            onClick={handleEditVideo}
            sx={{
              backgroundColor: '#008544',
              '&:hover': {
                backgroundColor: '#006633',
              }
            }}
          >
            Record Introduction
          </Button>
        )}
      </Paper>
    );
  }

  if (compact) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <Button
          variant="outlined"
          startIcon={<PlayIcon />}
          onClick={handlePlayVideo}
          size="small"
          sx={{
            borderColor: '#008544',
            color: '#008544',
            '&:hover': {
              borderColor: '#006633',
              backgroundColor: 'rgba(0, 133, 68, 0.04)',
            }
          }}
        >
          View Intro
        </Button>
        {showEditButton && (
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={handleEditVideo}
            size="small"
            sx={{
              borderColor: '#3b8c6e',
              color: '#3b8c6e',
              '&:hover': {
                borderColor: '#2a6b4f',
                backgroundColor: 'rgba(59, 140, 110, 0.04)',
              }
            }}
          >
            Edit
          </Button>
        )}
        
        {/* Video Dialog */}
        <Dialog
          open={videoDialogOpen}
          onClose={() => setVideoDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            {title}
            <IconButton
              aria-label="close"
              onClick={() => setVideoDialogOpen(false)}
              sx={{ position: "absolute", right: 8, top: 8 }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent>
            <video width="100%" controls autoPlay>
              <source src={judge.judgeIntro} type="video/mp4" />
              <source src={judge.judgeIntro} type="video/webm" />
              Your browser does not support the video tag.
            </video>
          </DialogContent>
        </Dialog>
      </Box>
    );
  }

  return (
    <Paper sx={{ p: 3, borderRadius: 2, boxShadow: 2 }}>
      <Typography variant="h6" sx={{ mb: 2, color: '#3b8c6e', display: 'flex', alignItems: 'center' }}>
        <VideoIcon sx={{ mr: 1 }} />
        {title}
      </Typography>
      
      <Box sx={{ position: 'relative', mb: 2 }}>
        <video 
          width="100%" 
          style={{ 
            borderRadius: '8px',
            maxHeight: '300px',
            objectFit: 'cover'
          }}
          poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23f0f0f0'/%3E%3Ctext x='50' y='50' text-anchor='middle' dy='.3em' font-family='Arial, sans-serif' font-size='14' fill='%23666'%3EClick to play%3C/text%3E%3C/svg%3E"
        >
          <source src={judge.judgeIntro} type="video/mp4" />
          <source src={judge.judgeIntro} type="video/webm" />
          Your browser does not support the video tag.
        </video>
        
        {/* Play overlay */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.3)',
            borderRadius: '8px',
            cursor: 'pointer',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
            }
          }}
          onClick={handlePlayVideo}
        >
          <PlayIcon sx={{ fontSize: 64, color: 'white' }} />
        </Box>
      </Box>

      <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
        <Button
          variant="contained"
          startIcon={<PlayIcon />}
          onClick={handlePlayVideo}
          sx={{
            backgroundColor: '#008544',
            '&:hover': {
              backgroundColor: '#006633',
            }
          }}
        >
          Play Video
        </Button>
        
        {showEditButton && (
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={handleEditVideo}
            sx={{
              borderColor: '#3b8c6e',
              color: '#3b8c6e',
              '&:hover': {
                borderColor: '#2a6b4f',
                backgroundColor: 'rgba(59, 140, 110, 0.04)',
              }
            }}
          >
            Re-record
          </Button>
        )}
      </Box>

      {/* Video Dialog */}
      <Dialog
        open={videoDialogOpen}
        onClose={() => setVideoDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {title}
          <IconButton
            aria-label="close"
            onClick={() => setVideoDialogOpen(false)}
            sx={{ position: "absolute", right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <video width="100%" controls autoPlay>
            <source src={judge.judgeIntro} type="video/mp4" />
            <source src={judge.judgeIntro} type="video/webm" />
            Your browser does not support the video tag.
          </video>
        </DialogContent>
      </Dialog>
    </Paper>
  );
};

export default JudgeIntroVideo;
